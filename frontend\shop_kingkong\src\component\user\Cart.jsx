import React from "react";
import { productThumbnails } from "./../../data/thumbnail";

const [product, setProduct] = useState(null);
const Cart = () => {
  return (
    <div className="min-h-screen bg-gray-50 pt-24 pb-12 ">
      <div className="max-w-6xl mx-auto px-6 lg:px-8">
        <div className="cart-section">
          <p className="font-bold text-center">GIỎ HÀNG</p>
          <hr className="mb-6" />
          <table className="w-full table-auto  ">
            <thead>
              <th>Sản phẩm</th>
              <th>Sản phẩm</th>
              <th>Giá</th>
              <th>Số lượng</th>
              <th>Tổng</th>
            </thead>
            <tbody>
              <tr>
                <td>{product.productThumbnails.mainImage}</td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
              </tr>
            </tbody>
          </table>
          <hr className="mb-6" />
        </div>
      </div>
    </div>
  );
};

export default Cart;
